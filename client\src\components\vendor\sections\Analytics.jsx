import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Row, Col, Typography, Space, message, Spin, Alert, Select, Empty
} from 'antd';
import {
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RiseOutlined,
  ReloadOutlined,
  WarningOutlined
} from '@ant-design/icons';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Doughnut } from 'react-chartjs-2';
import { dashboardApi } from '../../../services/vendorApi';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Add dayjs plugins
dayjs.extend(relativeTime);

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const { Text } = Typography;
const { Option } = Select;

// Custom INR Icon component
const INRIcon = () => (
  <span style={{ fontSize: '20px', fontWeight: 'bold' }}>₹</span>
);

// StatCard component matching admin design
const StatCard = ({
  title,
  value,
  icon,
  color,
  bgColor,
  suffix = '',
  precision = 0,
  subText = null,
  realTimeValue = null
}) => (
  <Card
    style={{
      borderRadius: '12px',
      border: 'none',
      boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
      overflow: 'hidden',
      height: '100%'
    }}
  >
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: '100%'
    }}>
      <div style={{ flex: 1 }}>
        <Text
          type="secondary"
          style={{
            fontSize: '12px',
            fontWeight: '500',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            display: 'block',
            marginBottom: '8px'
          }}
        >
          {title}
        </Text>
        <div style={{ display: 'flex', alignItems: 'baseline', marginBottom: '4px' }}>
          <Text style={{
            fontSize: '24px',
            fontWeight: '600',
            color: '#262626',
            lineHeight: '1'
          }}>
            {typeof value === 'number' ? value.toLocaleString('en-IN', {
              minimumFractionDigits: precision,
              maximumFractionDigits: precision
            }) : value}
            {suffix && <span style={{ fontSize: '14px', marginLeft: '4px' }}>{suffix}</span>}
          </Text>
        </div>
        {subText && (
          <Text type="secondary" style={{ fontSize: '11px', display: 'block' }}>
            {subText}
          </Text>
        )}
        {realTimeValue && (
          <div style={{ marginTop: '8px' }}>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {realTimeValue}
            </Text>
          </div>
        )}
      </div>
      <div style={{
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        background: bgColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0
      }}>
        {React.cloneElement(icon, { style: { fontSize: '24px', color } })}
      </div>
    </div>
  </Card>
);

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Format currency
  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  }, []);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      const response = await dashboardApi.getStats();

      if (response?.data?.success) {
        setDashboardData(response.data.data);
        setError(null);
      } else {
        throw new Error(response?.data?.message || 'Failed to fetch dashboard data');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');

      // Set fallback data
      setDashboardData({
        products: { totalProducts: 0, activeProducts: 0, averageRating: 0 },
        orders: { totalOrders: 0, totalRevenue: 0, averageOrderValue: 0, deliveredOrders: 0 },
        today: { todayOrders: 0, todayRevenue: 0 },
        recentOrders: [],
        topProducts: []
      });
    }
  };

  // Fetch analytics data
  const fetchAnalyticsData = async (period = selectedPeriod) => {
    try {
      const response = await dashboardApi.getAnalytics({ period, type: 'revenue' });

      if (response?.data?.success) {
        setAnalyticsData(response.data.data);
      } else {
        setAnalyticsData({ analytics: [], period, type: 'revenue' });
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setAnalyticsData({ analytics: [], period, type: 'revenue' });
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchDashboardData(),
      fetchAnalyticsData()
    ]);
    setRefreshing(false);
    message.success('Analytics refreshed');
  };

  // Handle period change
  const handlePeriodChange = (newPeriod) => {
    setSelectedPeriod(newPeriod);
    fetchAnalyticsData(newPeriod);
  };

  // Initialize data on mount
  useEffect(() => {
    // Set fallback data immediately to prevent loading state
    setDashboardData({
      products: { totalProducts: 0, activeProducts: 0, averageRating: 0 },
      orders: { totalOrders: 0, totalRevenue: 0, averageOrderValue: 0, deliveredOrders: 0, pendingOrders: 0, processingOrders: 0, shippedOrders: 0, cancelledOrders: 0 },
      today: { todayOrders: 0, todayRevenue: 0 },
      recentOrders: [],
      topProducts: []
    });
    setAnalyticsData({ analytics: [], period: '30d', type: 'revenue' });
    setLoading(false);

    const loadData = async () => {
      await Promise.all([
        fetchDashboardData(),
        fetchAnalyticsData()
      ]);
    };

    loadData();
  }, []);

  // Process chart data
  const revenueChartData = {
    labels: analyticsData?.analytics?.map(item => dayjs(item._id).format('MMM DD')) || [],
    datasets: [
      {
        label: 'Revenue (₹)',
        data: analyticsData?.analytics?.map(item => item.revenue || 0) || [],
        borderColor: '#40a9ff',
        backgroundColor: 'rgba(64, 169, 255, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#40a9ff',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4
      },
      {
        label: 'Orders',
        data: analyticsData?.analytics?.map(item => item.orders || 0) || [],
        borderColor: '#73d13d',
        backgroundColor: 'rgba(115, 209, 61, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#73d13d',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        yAxisID: 'y1'
      }
    ],
  };

  // Chart options
  const revenueChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#40a9ff',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            if (context.datasetIndex === 0) {
              return `Revenue: ₹${context.parsed.y.toLocaleString()}`;
            } else {
              return `Orders: ${context.parsed.y}`;
            }
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#8c8c8c'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        grid: {
          color: 'rgba(0, 0, 0, 0.06)'
        },
        ticks: {
          color: '#8c8c8c',
          callback: function(value) {
            return '₹' + value.toLocaleString();
          }
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: '#8c8c8c'
        }
      }
    }
  };

  // Order status distribution data
  const orderStatusData = {
    labels: ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
    datasets: [{
      data: dashboardData ? [
        dashboardData.orders?.pendingOrders || 0,
        dashboardData.orders?.processingOrders || 0,
        dashboardData.orders?.shippedOrders || 0,
        dashboardData.orders?.deliveredOrders || 0,
        dashboardData.orders?.cancelledOrders || 0
      ] : [],
      backgroundColor: [
        '#faad14', // Pending - yellow
        '#1890ff', // Processing - blue
        '#722ed1', // Shipped - purple
        '#52c41a', // Delivered - green
        '#f5222d'  // Cancelled - red
      ],
      borderWidth: 0,
      cutout: '60%'
    }]
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 15,
          generateLabels: function(chart) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label, i) => {
                const dataset = data.datasets[0];
                const value = dataset.data[i];
                return {
                  text: `${label}: ${value}`,
                  fillStyle: dataset.backgroundColor[i],
                  strokeStyle: dataset.backgroundColor[i],
                  lineWidth: 0,
                  pointStyle: 'circle'
                };
              });
            }
            return [];
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#40a9ff',
        borderWidth: 1,
        cornerRadius: 8
      }
    }
  };

  // Statistics cards data
  const mainStats = [
    {
      title: 'Total Revenue',
      value: dashboardData?.orders?.totalRevenue || 0,
      icon: <INRIcon />,
      color: '#52c41a',
      bgColor: 'rgba(82, 196, 26, 0.1)',
      precision: 2,
      realTimeValue: dashboardData?.today?.todayRevenue
        ? `Today: ₹${dashboardData.today.todayRevenue.toFixed(2)}`
        : null
    },
    {
      title: 'Total Orders',
      value: dashboardData?.orders?.totalOrders || 0,
      icon: <ShoppingCartOutlined />,
      color: '#1890ff',
      bgColor: 'rgba(24, 144, 255, 0.1)',
      realTimeValue: dashboardData?.today?.todayOrders
        ? `Today: ${dashboardData.today.todayOrders}`
        : null
    },
    {
      title: 'Total Products',
      value: dashboardData?.products?.totalProducts || 0,
      icon: <ShopOutlined />,
      color: '#722ed1',
      bgColor: 'rgba(114, 46, 209, 0.1)',
      subText: `Active: ${dashboardData?.products?.activeProducts || 0}`
    },
    {
      title: 'Average Rating',
      value: dashboardData?.products?.averageRating || 0,
      icon: <RiseOutlined />,
      color: '#fa8c16',
      bgColor: 'rgba(250, 140, 22, 0.1)',
      precision: 1,
      suffix: '/ 5'
    }
  ];

  // Loading state
  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <span>Loading vendor analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Error Alert */}
      {error && (
        <Alert
          message="Data Loading Issue"
          description={error}
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* Main Statistics Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {mainStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <StatCard {...stat} />
          </Col>
        ))}
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexWrap: 'wrap',
                gap: '8px'
              }}>
                <span>Revenue & Orders Trend</span>
                <Space>
                  <Select
                    value={selectedPeriod}
                    onChange={handlePeriodChange}
                    style={{ width: 140 }}
                    size="small"
                  >
                    <Option value="7d">Last 7 days</Option>
                    <Option value="30d">Last 30 days</Option>
                    <Option value="90d">Last 90 days</Option>
                  </Select>
                  <ReloadOutlined
                    onClick={handleRefresh}
                    spin={refreshing}
                    style={{ cursor: 'pointer', color: '#1890ff' }}
                  />
                </Space>
              </div>
            }
            style={{
              borderRadius: '12px',
              border: 'none',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}
          >
            <div style={{ height: '300px' }}>
              {loading || refreshing ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%'
                }}>
                  <Spin tip="Loading chart data..." />
                </div>
              ) : analyticsData?.analytics?.length > 0 ? (
                <Line data={revenueChartData} options={revenueChartOptions} />
              ) : (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  flexDirection: 'column'
                }}>
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="No revenue data available"
                    style={{ margin: 0 }}
                  />
                </div>
              )}
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card
            title="Order Status Distribution"
            style={{
              borderRadius: '12px',
              border: 'none',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}
          >
            <div style={{ height: '300px' }}>
              {loading || refreshing ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%'
                }}>
                  <Spin tip="Loading chart data..." />
                </div>
              ) : dashboardData?.orders?.totalOrders > 0 ? (
                <Doughnut data={orderStatusData} options={doughnutOptions} />
              ) : (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  flexDirection: 'column'
                }}>
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="No order data available"
                    style={{ margin: 0 }}
                  />
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Analytics;
